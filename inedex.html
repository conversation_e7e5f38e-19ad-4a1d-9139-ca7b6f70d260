<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ขั้นตอนการ e-Procurement</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Custom styles can be added here if needed, or inline Tailwind classes are sufficient */
        body {
            font-family: 'Sarabun', 'TH Sarabun New';
        }
        .step-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body class="bg-gray-100 p-8 font-sans">

    <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-xl p-8">
        <h1 class="text-3xl font-bold text-center text-blue-700 mb-8">
            ขั้นตอนการ e-Procurement
        </h1>

        <div class="space-y-6">

            <div id="step1" class="step-card bg-blue-50 border-l-4 border-blue-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer">
                <div class="flex-shrink-0 w-10 h-10 bg-blue-500 text-white rounded-full flex items-center justify-center text-xl font-bold">1</div>
                <div>
                    <h2 class="text-xl font-semibold text-blue-800">ล็อกอินระบบ e-Procurement</h2>
                    <p class="text-gray-700 mt-2">
                        เข้าเว็บไซต์ <a href="https://eprocurement.pea.co.th" class="text-blue-500 " target="_blank">eprocurement.pea.co.th</a>
                        จากนั้นกรอกชื่อผู้ใช้งานและรหัสผ่านเพื่อล็อกอินระบบ
                    </p>
                </div>
            </div>

            <div id="step2" class="step-card bg-green-50 border-l-4 border-green-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer">
                <div class="flex-shrink-0 w-10 h-10 bg-green-500 text-white rounded-full flex items-center justify-center text-xl font-bold">2</div>
                <div>
                    <h2 class="text-xl font-semibold text-green-800">การประกาศซื้อจ้าง (อื้นๆ)</h2>
                    <p class="text-gray-700 mt-2">
                        เมื่อเข้าระบบแล้ว ให้คลิกที่ "<strong class="font-medium">การประกาศซื้อจ้าง (อื้นๆ)</strong>"
                    </p>
                </div>
            </div>

            <div id="step3" class="step-card bg-yellow-50 border-l-4 border-yellow-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer">
                <div class="flex-shrink-0 w-10 h-10 bg-yellow-500 text-white rounded-full flex items-center justify-center text-xl font-bold">3</div>
                <div>
                    <h2 class="text-xl font-semibold text-yellow-800">วงเงินไม่เกิน 100,000 บาท</h2>
                    <p class="text-gray-700 mt-2">
                        ภายใต้ในขั้นตอน 2 ให้ "<strong class="font-medium">วงเงินไม่เกิน 100,000 บาท</strong>"
                    </p>
                </div>
            </div>

            <div id="step4" class="step-card bg-purple-50 border-l-4 border-purple-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer">
                <div class="flex-shrink-0 w-10 h-10 bg-purple-500 text-white rounded-full flex items-center justify-center text-xl font-bold">4</div>
                <div>
                    <h2 class="text-xl font-semibold text-purple-800">วงเงินไม่เกิน 100,000 บาท</h2>
                    <p class="text-gray-700 mt-2">
                        จากนั้นให้ "<strong class="font-medium">วงเงินไม่เกิน 100,000 บาท</strong>"
                    </p>
                </div>
            </div>

            <div id="step5" class="step-card bg-red-50 border-l-4 border-red-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer">
                <div class="flex-shrink-0 w-10 h-10 bg-red-500 text-white rounded-full flex items-center justify-center text-xl font-bold">5</div>
                <div>
                    <h2 class="text-xl font-semibold text-red-800">กดปุ่ม "ต่อไป" และกรอกรายละเอียด</h2>
                    <p class="text-gray-700 mt-2">
                        กดปุ่ม "<strong class="font-medium">ต่อไป</strong>" จากนั้นกรอกรายละเอียดในช่องที่มี <span class="text-red-500 font-bold">*</span> ให้ครบถ้วน เมื่อเสร็จแล้วให้กดปุ่ม "<strong class="font-medium">ต่อไป</strong>"
                    </p>
                </div>
            </div>

        </div>
    </div>

    <div id="popup-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden">
        <div class="bg-white p-8 rounded-lg shadow-lg max-w-5xl w-full relative">
            <button id="close-popup" class="absolute top-3 right-3 text-gray-600 hover:text-gray-900 text-2xl font-bold">&times;</button>
            <h3 id="popup-title" class="text-2xl font-bold mb-4 text-center"></h3>
            <div class="flex items-center justify-center">
                <button id="prev-btn" class="absolute left-0 transform -translate-x-1/2 hidden text-gray-600 hover:text-gray-900 text-2xl font-bold">&lt;</button>
                <img id="popup-image" src="" alt="Step Image" class="w-full h-auto mb-4 rounded">
                <button id="next-btn" class="absolute right-0 transform translate-x-1/2 hidden text-gray-600 hover:text-gray-900 text-2xl font-bold">&gt;</button>
            </div>
            <div id="image-counter" class="absolute bottom-0 left-1/2 transform -translate-x-1/2 hidden text-gray-600">
                <span id="current-image">1</span> / <span id="total-images">1</span>
            </div>
            <p id="popup-description" class="text-gray-800 text-center"></p>
        </div>
    </div>

    <script>
        // JavaScript for Pop-up functionality (Example - needs to be expanded for each step and image paths)
        const stepCards = document.querySelectorAll('.step-card');
        const popupOverlay = document.getElementById('popup-overlay');
        const popupTitle = document.getElementById('popup-title');
        const popupImage = document.getElementById('popup-image');
        const popupDescription = document.getElementById('popup-description');
        const closePopupBtn = document.getElementById('close-popup');
        const prevBtn = document.getElementById('prev-btn');
        const nextBtn = document.getElementById('next-btn');
        const imageCounter = document.getElementById('image-counter');
        const currentImageSpan = document.getElementById('current-image');
        const totalImagesSpan = document.getElementById('total-images');

        const stepData = {
            step1: {
                title: 'ล็อกอินระบบ e-Procurement',
                images: ['Image/001.png'],
                description: 'หน้าจอเข้าระบบ e-Procurement'
            },
            step2: {
                title: 'การประกาศซื้อจ้าง (อื้นๆ)',
                images: ['Image/002.png'],
                description: 'การประกาศซื้อจ้าง'
            },
            step3: {
                title: 'วงเงินไม่เกิน 100,000 บาท',
                images: ['Image/003.png'],
                description: 'หน้าจอผล'
            },
            step4: {
                title: 'วงเงินไม่เกิน 100,000 บาท',
                images: ['Image/004.png'],
                description: 'หน้าจอผล'
            },
            step5: {
                title: 'กรอกรายละเอียดและผล',
                images: ['Image/005.1.png', 'Image/005.2.png'],
                description: 'หน้าจอกรอกข้อมูลและปุ่ม'
            },
            step6: {
                title: 'ขออนุมัติประกาศชนะรายไตรมาส',
                images: ['Image/006.png'],
                description: 'หน้าจอขออนุมัติประกาศชนะ'
            },
            step7: {
                title: 'กรอกรายละเอียดและส่งอนุมัติ',
                images: ['Image/006.png'],
                description: 'หน้าจอกรอกข้อมูลและปุ่มส่งอนุมัติ'
            }
        };

        let currentImageIndex = 0;
        let currentStepImages = [];

        stepCards.forEach(card => {
            card.addEventListener('click', () => {
                const stepId = card.id;
                const data = stepData[stepId];

                if (data) {
                    currentStepImages = data.images;
                    currentImageIndex = 0;
                    
                    popupTitle.textContent = data.title;
                    updateImage();
                    popupDescription.textContent = data.description;
                    
                    // Show/hide navigation based on image count
                    if (currentStepImages.length > 1) {
                        prevBtn.classList.remove('hidden');
                        nextBtn.classList.remove('hidden');
                        imageCounter.classList.remove('hidden');
                        totalImagesSpan.textContent = currentStepImages.length;
                    } else {
                        prevBtn.classList.add('hidden');
                        nextBtn.classList.add('hidden');
                        imageCounter.classList.add('hidden');
                    }
                    
                    popupOverlay.classList.remove('hidden');
                }
            });
        });

        function updateImage() {
            popupImage.src = currentStepImages[currentImageIndex];
            currentImageSpan.textContent = currentImageIndex + 1;
        }

        prevBtn.addEventListener('click', () => {
            currentImageIndex = currentImageIndex > 0 ? currentImageIndex - 1 : currentStepImages.length - 1;
            updateImage();
        });

        nextBtn.addEventListener('click', () => {
            currentImageIndex = currentImageIndex < currentStepImages.length - 1 ? currentImageIndex + 1 : 0;
            updateImage();
        });

        closePopupBtn.addEventListener('click', () => {
            popupOverlay.classList.add('hidden');
        });

        popupOverlay.addEventListener('click', (e) => {
            if (e.target === popupOverlay) {
                popupOverlay.classList.add('hidden');
            }
        });
    </script>

</body>
</html>
